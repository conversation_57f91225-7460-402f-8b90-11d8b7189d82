// @import '@/styles/btn.scss';
.Matting {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  background-color: #f6f6f6;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    } /* 动画开始时，图片旋转0度 */
    100% {
      transform: rotate(360deg);
    } /* 动画结束时，图片旋转360度 */
  }

  section {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    height: 100%;
  }

  .detail {
    position: relative;

    .matting-actions {
      padding: 10px;
      display: flex;
      justify-content: center;
      background-color: #f5f7fa;
      border-bottom: 1px solid #e4e7ed;
      z-index: 10;
    }

    .box {
      position: absolute;
      margin: 0 47px;
      width: calc(100% - 289px - 94px);
      height: calc(100% - 202px);
      box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.07);
      // @include common-dialog;
    }

    // 换背景
    .background-box {
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1;

      .background-container {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
        margin: auto; /* Center in parent */
        max-width: 100%; /* Ensure it doesn't exceed parent width */
        max-height: 100%; /* Ensure it doesn't exceed parent height */

        .background-image {
          position: relative;
          max-width: 100%;
          max-height: 100%;
          -webkit-user-drag: none;
          z-index: 0;
        }
        .matting-image {
          position: absolute;
          max-width: 100%;
          max-height: 100%;
          -webkit-user-drag: none;
          z-index: 1;
        }
      }

      .matting-progress {
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #fff;
        border-radius: 50%;
        box-shadow: 0px 2px 9px 0px rgba(0, 0, 0, 0.31);
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10;
      }
    }

    // 改尺寸
    :deep(.cropper-box) {
      overflow: hidden;

      img {
        // max-width: 100%;
        // max-height: 100%;
        -webkit-user-drag: none;
      }

      .cropper-crop-box {
        position: relative;
        box-shadow: 0px 2px 9px 0px rgba(0, 0, 0, 0.31);
        .cropper-view-box {
          outline: 1px solid #ffffff;
        }
        /* 为裁剪框添加网格线 */
        &::before,
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          pointer-events: none;
          z-index: 1;
        }
        /* 垂直网格线 */
        &::before {
          background-image: linear-gradient(
            to right,
            #fff 1px,
            transparent 1px
          );
          background-size: 33.33% 100%;
        }
        /* 水平网格线 */
        &::after {
          background-image: linear-gradient(
            to bottom,
            #fff 1px,
            transparent 1px
          );
          background-size: 100% 33.33%;
        }
      }

      .cropper-line {
        background-color: white;
      }

      .cropper-dashed {
        border: 0 solid #fff;
        border-left-width: 1px;
        border-right-width: 1px;
        border-bottom-width: 1px;
        border-top-width: 1px;
      }

      .cropper-point {
        opacity: 1;
        background-color: white;
      }

      .point-n,
      .point-s {
        display: none;
      }

      .point-nw {
        // 左上
        // width: 6px;
        // height: 26px;
        // background: #ffffff;

        width: 26px;
        height: 26px;
        border: 6px solid #ffffff;
        border-radius: 0;
        border-right-width: 0;
        border-bottom-width: 0;
        background-color: transparent;
      }
      .point-ne {
        // 右上
        width: 26px;
        height: 26px;
        border: 6px solid #ffffff;
        border-radius: 0;
        border-left-width: 0;
        border-bottom-width: 0;
        background-color: transparent;
      }
      .point-w {
        // 左中
        width: 6px;
        height: 26px;
        background: #ffffff;
        border-radius: 0;
        margin-top: -12px;
      }
      .point-e {
        // 右中
        width: 6px;
        height: 26px;
        background: #ffffff;
        border-radius: 0;
        margin-top: -12px;
      }
      .point-sw {
        // 左下
        width: 26px;
        height: 26px;
        border: 6px solid #ffffff;
        border-radius: 0;
        border-top-width: 0;
        border-right-width: 0;
        background-color: transparent;
      }
      .point-se {
        // 右下
        width: 26px;
        height: 26px;
        border: 6px solid #ffffff;
        border-radius: 0;
        border-top-width: 0;
        border-left-width: 0;
        background-color: transparent;
      }
    }

    // 手动精修
    .manual-refine-box {
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;

      .manual-refine-canvas {
        cursor: crosshair;
        background: transparent;
        transform-origin: center center;

        // 当处于拖动模式时改变光标
        &.drag-mode {
          cursor: grab;

          &:active {
            cursor: grabbing;
          }
        }
      }

      .origin-box {
        cursor: default;
        background: transparent;
        transform-origin: center center;
        -webkit-user-drag: none;

        .origin-matting-img {
          width: 100%;
          height: 100%;
          object-fit: contain;
          pointer-events: none;
        }
      }

      .info {
        position: fixed;
        bottom: 45px;
        user-select: none;

        height: 40px;
        font-weight: 500; // electron
        font-size: 14px;
        color: #333;
        line-height: 20px;
        border-radius: 8px;
        background-color: #fff;

        .origin-info {
          padding: 0px 37px;
          text-wrap: nowrap;
          cursor: pointer;
        }

        .result-info {
          padding: 0px 30px;
          text-wrap: nowrap;
          cursor: pointer;
        }

        .selected {
          border-radius: 6px;
          color: #fff;
          background: #389bfd;
          transition: all 0.5s;
        }
      }
    }

    .watermark-box {
      overflow: hidden;
      pointer-events: none;
      z-index: 3;
    }
  }
}

// Matting process styles
.matting-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.matting-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
  padding: 20px;
}

.upload-area {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.processing-area {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.result-area {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.image-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
}

.image-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  max-width: 300px;
}

.image-label {
  font-weight: bold;
}

.result-image {
  max-width: 100%;
  max-height: 300px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

.matting-tools {
  padding: 20px;
  background-color: #f5f7fa;
  border-top: 1px solid #e4e7ed;
}

.tool-section {
  margin-bottom: 20px;
}

.color-picker {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.background-upload {
  margin-top: 10px;
}
