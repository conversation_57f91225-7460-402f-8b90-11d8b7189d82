# 抠图功能修复测试指南

## 修复内容

### 1. 人像描边问题修复
**问题**: 人像区域出现不自然的描边效果
**修复**: 
- 将白色区域检测阈值保持为 `> 200`，确保只在明确的保留区域显示红色蒙层
- 明确设置混合模式，确保绘制效果一致

**测试步骤**:
1. 上传一张人像图片
2. 进入手动精修模式
3. 观察人像区域的红色蒙层是否有不自然的描边
4. **预期结果**: 红色蒙层应该紧贴人像边缘，没有额外的描边效果

### 2. 画笔重复绘制问题修复
**问题**: 画笔在同一区域绘画时，颜色会越来越深
**修复**: 
- 添加距离检查机制，只有当移动距离大于画笔大小的1/4时才绘制
- 保持原有的绘制逻辑，确保画笔颜色正确

**测试步骤**:
1. 进入手动精修模式
2. 选择"保留"画笔
3. 在同一区域反复绘制或缓慢移动
4. **预期结果**: 画笔应该保持红色半透明效果，不会变成黑色或白色，同一区域不会因重复绘制而颜色加深

### 3. 切换原图/效果图历史记录丢失问题修复
**问题**: 切换原图和效果图时，撤销、恢复、重置记录丢失
**修复**: 
- 添加对 `isOrigin` 状态变化的监听
- 确保切换时保持历史记录的连续性

**测试步骤**:
1. 进入手动精修模式
2. 使用画笔进行一些绘制操作
3. 点击"原图"切换到原图视图
4. 再点击"效果图"切换回效果图视图
5. 尝试使用撤销、恢复、重置功能
6. **预期结果**: 撤销、恢复、重置功能应该正常工作，历史记录不应该丢失

### 4. 透明度调整
**问题**: #F60000 透明度从0.5改成0.3
**修复**: 
- 将所有红色蒙层的透明度从50%调整为30%
- 包括初始蒙层显示和画笔绘制时的透明度

**测试步骤**:
1. 进入手动精修模式
2. 观察初始红色蒙层的透明度
3. 使用画笔绘制，观察绘制区域的透明度
4. **预期结果**: 红色蒙层应该比之前更透明，更容易看到下方的原始图像

## 技术实现细节

### 防重复绘制机制
```typescript
// 检查移动距离，避免重复绘制
const dx = x - lastX.value
const dy = y - lastY.value
const distance = Math.sqrt(dx * dx + dy * dy)

// 如果移动距离太小，跳过绘制
if (distance < batchStore.brushSize / 4) {
  return
}
```

### 透明度调整
```typescript
// 初始蒙层透明度：30%
overlayPixels[i + 3] = 77  // A (半透明，30%)

// 画笔绘制透明度：30%
ctx.strokeStyle = 'rgba(246, 0, 0, 0.3)' // #F60000 半透明
```

### 历史记录保持机制
```typescript
// 监听原图/效果图切换
watch(() => isOrigin.value, (newIsOrigin, oldIsOrigin) => {
  // 切换时不清空历史记录，只是更新显示状态
  // 确保历史记录状态同步到组件
})
```

## 注意事项

1. 测试时请确保使用最新的代码版本
2. 如果发现任何问题，请检查浏览器控制台的日志输出
3. 建议在不同的浏览器中测试以确保兼容性
4. 测试时可以使用不同大小和类型的图片

## 预期改进效果

1. **更自然的视觉效果**: 人像区域的红色蒙层应该更加自然，没有不必要的描边
2. **一致的绘制体验**: 画笔绘制应该更加流畅，颜色深度一致，不会变成黑色或白色
3. **稳定的历史记录**: 切换视图时不会丢失操作历史，用户体验更好
4. **更合适的透明度**: 30%的透明度让用户更容易看到原始图像细节
