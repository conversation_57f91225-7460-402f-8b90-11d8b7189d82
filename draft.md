1、手动精修的图片，更新到了换背景，但没有更新到改尺寸里面
2、manual-refine-canvas生成的是人像为白色、其他为黑色的画布图片，
实际需要的是全部显示 currentImg.file，根据 mattingImage 确定人像部分，
在人像处加一个透明蒙层（颜色可以为#F60000 半透明），
画笔保留时根据画笔处增加这个通明蒙层，画笔擦除时移除画笔处的透明蒙层，
将蒙层区域的图，更新到mattingImage里面

5、window.addEventListener('mattingImageUpdated' 需要补充解绑

没有实现我的需求
1、需要的是manual-refine-canvas移除黑白画图模式
2、首先使用 currentImg.file 填满画布（类似之前的黑白总和），currentImg.file是mattingImage未抠图之前的图片
3、其次根据 mattingImage 确定人像部分，在人像处加一个透明蒙层（颜色可以为#F60000 半透明，这个透明蒙层就是之前的白色部分）
4、关于画笔，画笔保留 增加这个透明蒙层（颜色可以为#F60000 半透明），画笔擦除 擦除透明蒙层部分

现在的效果还是原来的黑白模式，只是画笔拖动时重新生成了白色局部图

效果实现了，但是问题比较多
1、首次进来，颜色可以为#F60000 半透明蒙版 没有包含完整的人像
2、每次画笔拖动时，都会重新生成透明蒙版，应该做增量
3、画笔涂画的过程中也应该是半透明的，现在是非透明的
4、画笔速度快时，生成的区块是非连续的
5、撤销、恢复、重置 失效了
6、又发生了手动精修图片没有同步到改尺寸


1、首次进来，颜色可以为#F60000 半透明蒙版 没有包含完整的人像
2、每次画笔拖动时，都会重新生成透明蒙版，应该做增量
3、画笔涂画的过程中也应该是半透明的，现在是非透明的
4、画笔速度快时，生成的区块是非连续的
5、撤销、恢复、重置 失效了
6、又发生了手动精修图片没有同步到改尺寸

1、已解决
2、3 解决部分，问题：① 画笔所画区域发生了重复渲染，堆叠导致看起来像非透明，实际上只需要绘制一次，看起来就很透明了（快速拖动时绘制的就透明） ② 快速拖动时绘制的区域正常是没有渲染到mattingImage里面的
5、撤销、恢复、重置 存在丢失的情况，没能记录完整
6、画笔所绘区域生成的图又变成了黑色，上一版是好的

1、人像处为什么进行了描边？
2、画笔绘画时，同一区域只需要绘画一次，再次过来时，就不需要再绘画了，现在是不断绘画直到颜色越来越深(注意不要又改成了黑色或白色)
3、切换原图、效果图时 导致撤销、恢复、重置 记录丢失
4、将透明度